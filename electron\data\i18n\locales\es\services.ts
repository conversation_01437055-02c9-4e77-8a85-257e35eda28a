/**
 * Relacionado con servicios - Español
 */
export default {
  // Servicio de licencia
  license: {
    cacheResult: "Usando resultado en caché",
    verificationTime: "Tiempo de verificación",
    verificationFailed: "Verificación fallida, tiempo",
    businessErrorInfo: "Obtener información de error de negocio"
  },

  // Servicio multiplataforma
  cross: {
    startGoService: "Iniciando servicio Go...",
    goServiceStartSuccess: "Servicio Go iniciado exitosamente, tiempo",
    goServiceStartFailed: "Inicio de servicio Go fallido, tiempo",
    startPythonService: "Iniciando servicio Python...",
    pythonServiceStartSuccess: "Servicio Python iniciado exitosamente, tiempo",
    pythonServiceStartFailed: "Inicio de servicio Python fallido, tiempo",
    optimizeStartParams: "Optimizar parámetros de inicio"
  },

  // Servicio base
  base: {
    getClientStart: "Comenzar a obtener cliente de dispositivo, ID de dispositivo",
    deviceNotFound: "Información de dispositivo no encontrada, ID de dispositivo",
    deviceNotConnected: "Dispositivo no conectado o cliente inválido, ID de dispositivo",
    getClientSuccess: "Cliente de dispositivo obtenido exitosamente, ID de dispositivo"
  },

  // Servicio de conexión de dispositivo
  deviceConnect: {
    deviceConnected: "Dispositivo conectado",
    connectionAttempt: "Intento de conexión"
  },

  // Servicio de información de dispositivo
  deviceInfo: {
    getDeviceInfoStart: "Comenzar a obtener información de dispositivo",
    exportStart: "Comenzar exportación de información de dispositivo",
    exportSuccess: "Exportación de información de dispositivo exitosa"
  },

  // Servicio de configuración
  configure: {
    getConfigureList: "Obtener lista de configuración",
    addConfigure: "Agregar configuración",
    setId: "Establecer ID",
    projectNotExists: "El proyecto no existe",
    duplicateName: "Nombre duplicado, por favor reingrese",
    addConfigureException: "Excepción al agregar configuración",
    projectNotFound: "Proyecto no encontrado",
    projectPathNotFound: "Ruta de proyecto no encontrada",
    replaceWithNew: "Reemplazar con nuevo contenido",
    projectNotFoundShort: "Proyecto no encontrado",
    operationTypeIncorrect: "Tipo de operación incorrecto, valores permitidos son [project,hmi]",
    renameConfigureException: "Excepción al renombrar configuración",
    getConfigureListException: "Excepción al obtener lista de configuración",
    configureSaveException: "Excepción al guardar configuración",
    openConfigureFolder: "Abrir carpeta de configuración"
  }
};
