import ExcelJS from "exceljs";
import fs from "fs";
import path from "path";
import { logger } from "ee-core/log";
import { ExcelStyle, createStyles } from "../interface/debug/excelStyles"; // 引入样式创建函数
import { Column } from "../interface/debug/exportTypes";
import { ImportResult, ParseOptions, SheetData } from "../interface/debug/sheetDara";
import { t } from "../data/i18n/i18n";

export class ExcelExporter {
  private styles: ExcelStyle;

  constructor() {
    this.styles = createStyles();
  }

  public async exportToExcel<T>(
    data: T[],
    columns: Column[],
    filePath: string,
    sheetName: string = "数据"
  ): Promise<void> {
    if (path.extname(filePath).toLowerCase() !== ".xlsx") {
      throw new Error(t("errors.invalidFilePath"));
    }

    const dirPath = path.dirname(filePath);

    // 确保目录存在，如果不存在则创建目录
    if (!fs.existsSync(dirPath)) {
      try {
        fs.mkdirSync(dirPath, { recursive: true });
      } catch (error) {
        logger.error("[excelUtils] Failed to create directory", { error });
        throw new Error(t("errors.failedToCreateDirectory"));
      }
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    // 添加表头
    worksheet.columns = columns;

    // 设置表头样式和背景色
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.style = this.styles.header;
    });

    // 添加数据并设置边框
    data.forEach((item) => {
      const row = worksheet.addRow(item);
      row.eachCell((cell) => {
        cell.style = this.styles.cell;
      });
    });

    // 保存Excel文件
    try {
      await workbook.xlsx.writeFile(filePath);
    } catch (error) {
      logger.error("[excelUtils] exportToExcel failed", { error });
      throw new Error(t("errors.failedToExportData"));
    }
  }

  // 导出多个sheet
  public async exportToMutilSheetExcel(
    sheets: SheetData[],
    columns: Column[],
    filePath: string
  ): Promise<void> {
    if (path.extname(filePath).toLowerCase() !== ".xlsx") {
      throw new Error(t("errors.invalidFilePath"));
    }
    const dirPath = path.dirname(filePath);

    // 确保目录存在，如果不存在则创建目录
    if (!fs.existsSync(dirPath)) {
      try {
        fs.mkdirSync(dirPath, { recursive: true });
      } catch (error) {
        logger.error("[excelUtils] Failed to create directory", { error });
        throw new Error(t("errors.failedToCreateDirectory"));
      }
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();

    sheets.forEach((sheet) => {
      const sheetName = sheet.sheetName;
      const data = sheet.data;

      const worksheet = workbook.addWorksheet(sheetName);
      // 添加表头
      worksheet.columns = columns;

      // 设置表头样式和背景色
      const headerRow = worksheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.style = this.styles.header;
      });

      // 添加数据并设置边框
      data.forEach((item) => {
        const row = worksheet.addRow(item);
        row.eachCell((cell) => {
          cell.style = this.styles.cell;
        });
      });
    });
    // 保存Excel文件
    try {
      await workbook.xlsx.writeFile(filePath);
    } catch (error) {
      logger.error("[excelUtils] exportToExcel failed", { error });
      throw new Error(t("errors.failedToExportData"));
    }
  }

  public async parseExcel(
    filePath: string,
    keyMapping: { [key: string]: string } = {},
    sheetName?: string
  ): Promise<any[]> {
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);

      // 获取指定工作表
      let worksheet;
      if (sheetName) {
        worksheet = workbook.getWorksheet(sheetName);
      } else {
        worksheet = workbook.getWorksheet(1);
      }
      if (!worksheet) {
        throw new Error(t("errors.worksheetNotFound"));
      }

      // 获取表头
      const headers = worksheet.getRow(1).values as string[];
      if (!headers || headers.length === 0) {
        throw new Error(t("errors.noHeaders"));
      }

      // 获取数据行
      const rows = worksheet.getRows(2, worksheet.rowCount - 1);

      if (!rows || rows.length === 0) {
        return []; // 如果没有数据行，返回空数组
      }

      const parsedData = rows.map((row) => {
        const obj: { [key: string]: any } = {};
        const rowValues = row.values;
        if (Array.isArray(rowValues)) {
          rowValues.forEach((value, index) => {
            if (headers[index]) {
              const mappedKey = keyMapping[headers[index]] || headers[index];
              obj[mappedKey] = value;
            }
          });
        }
        return obj;
      });

      return parsedData;
    } catch (error) {
      if (error instanceof Error) {
        logger.error("[excelUtils] Failed to parse Excel file", { error });
        throw new Error(`Failed to parse Excel file: ${error.message}`);
      } else {
        logger.error(
          "[excelUtils] Failed to parse Excel file with an unknown error",
          { error }
        );
        throw new Error(t("errors.parseExcelUnknownError"));
      }
    }
  }

  // 修复后的完整 parseMultiSheets 方法
  public async parseMultiSheets<T = Record<string, any>>(
    filePath: string,
    options: ParseOptions = {}
  ): Promise<ImportResult> {
    try {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);

      // 修复原图 workbook.work → workbook.worksheets
      return workbook.worksheets
        .filter(
          (worksheet) => !options.skipEmptySheet || worksheet.actualRowCount > 1
        )
        .map((worksheet) => {
          const headerRow = worksheet.getRow(1);
          if (!headerRow || headerRow.actualCellCount === 0) {
            logger.info(
              `Sheet [${worksheet.name}] ${t("errors.missingHeaders")}`
            );
            return {};
          }

          // 处理表头并应用 keyMapping
          const headers = ((headerRow.values as ExcelJS.CellValue[]) ?? [])
            .slice(1)
            .map((v) => {
              const rawHeader = String(v ?? "");
              return options.keyMapping?.[rawHeader] || rawHeader;
            });

          // 数据行处理（集成 keyMapping）
          const data: T[] = [];
          worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            if (rowNumber === 1) return;

            const rowData = headers.reduce((acc, header, index) => {
              const cell = row.getCell(index + 1);
              const cellValue = cell.isMerged
                ? cell.master?.text?.trim() || ""
                : cell.text?.trim() || "";
              acc[header] = cellValue;
              return acc;
            }, {} as T);

            data.push(rowData);
          });

          return { grpname: worksheet.name, data };
        });
    } catch (error) {
      throw new Error(t("errors.excelParseFailed"));
    }
  }
}
