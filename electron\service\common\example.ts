import { logger } from 'ee-core/log';
import { t } from "../../data/i18n/i18n";

// example service
class ExampleService {

  async test(args: any): Promise<{ status: string; params: any }> {
    let obj = {
      status: t("common.success"),
      params: args,
    };
    logger.info('ExampleService obj:', obj);
    return obj;
  }
}
ExampleService.toString = () => '[class ExampleService]';
const exampleService = new ExampleService();

export {
  ExampleService,
  exampleService
};