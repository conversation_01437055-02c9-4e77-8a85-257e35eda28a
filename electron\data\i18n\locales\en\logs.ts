/**
 * Log messages - English
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList entry parameters",
    getCommonReportListReturn: "getCommonReportList return",
    getCommonReportListError: "getCommonReportList exception",
    cancelUploadStart: "Cancel wave file upload start",
    cancelUploadError: "Cancel wave file upload exception",
    openWaveFileStart: "Open wave file start",
    openWaveFileError: "Open wave file exception"
  },
  configureService: {
    getConfigureListError: "Get configuration list exception",
    loadConfigureError: "Load configuration exception"
  },
  paramService: {
    getDiffParamComplete: "Comparison complete, difference groups",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo entry parameters",
    getParamInfoReturn: "getParamInfo return",
    getParamInfoError: "getParamInfo exception",
    startGetParamInfo: "Start getting parameter settings, pagination",
    getAllParamInfoStart: "Start getting all parameter settings",
    getAllParamInfoSuccess: "Successfully got all parameter settings, total",
    modifyParamStart: "Start modifying parameter settings",
    validateParam: "Validate parameter item",
    validateFailed: "Parameter validation failed",
    validatePassed: "Parameter validation passed, ready to send",
    setTimeout: "Set timeout",
    sendResponse: "Send response",
    modifySuccess: "Modification successful",
    sendFailed: "Send failed",
    businessError: "Business error",
    getAllDiffParamStart: "Start batch parameter comparison",
    excelParseFailed: "Excel parsing failed",
    csvParseFailed: "CSV parsing failed",
    xmlParseFailed: "XML parsing failed",
    fileParseComplete: "File parsing complete",
    getDiffParamStart: "Start single group parameter comparison",
    diffComplete: "Comparison complete, difference items",
    importParamStart: "Start importing parameter settings",
    paramReady: "Parameters ready to send",
    importSuccess: "Import successful",
    exportAllParamStart: "Start exporting all parameter settings",
    exportComplete: "Export complete",
    exportParamStart: "Start exporting group parameters",
    getGroupItemsStart: "Get group parameter items",
    getParamValueFailed: "Failed to get parameter value",
    getGroupItemsComplete: "Get complete, parameter items",
    getAllGroupItemsStart: "Get all group parameter items",
    groupParamCount: "Group: {group}, parameter items: {count}",
    getCurrentRunAreaStart: "Get current run area",
    getCurrentRunAreaSuccess: "Get successful",
    getCurrentRunAreaFailed: "Get failed",
    selectRunAreaStart: "Select setting area",
    runAreaEmpty: "Setting area cannot be empty",
    selectRunAreaSuccess: "Select successful",
    selectRunAreaFailed: "Select failed"
  },
  debugInfoMenuService: {
    initialized: "Initialization complete",
    getDebugInfoEntry: "getDebugInfo entry parameters",
    getTreeMenuError: "getTreeMenu exception",
    getTreeMenuComplete: "Processing complete, menu count"
  }
};
