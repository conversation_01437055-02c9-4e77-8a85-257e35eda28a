/**
 * Log messages - English
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList entry parameters",
    getCommonReportListReturn: "getCommonReportList return",
    getCommonReportListError: "getCommonReportList exception",
    cancelUploadStart: "Cancel wave file upload start",
    cancelUploadError: "Cancel wave file upload exception",
    openWaveFileStart: "Open wave file start",
    openWaveFileError: "Open wave file exception",
    getGroupReportStart: "Get group report start",
    getGroupReportError: "Get group report exception",
    getOperateReportStart: "Get operate report start",
    getOperateReportError: "Get operate report exception",
    getAuditReportStart: "Get audit report start",
    getAuditReportError: "Get audit report exception",
    exportCommonReportStart: "Export common report start",
    exportCommonReportError: "Export common report exception",
    clearReportStart: "Clear report start",
    clearReportError: "Clear report exception",
    refreshReportStart: "Refresh common report start",
    refreshReportError: "Refresh common report exception",
    refreshGroupReportStart: "Refresh group report start",
    refreshGroupReportError: "Refresh group report exception",
    refreshOperateReportStart: "Refresh operate report start",
    refreshOperateReportError: "Refresh operate report exception",
    refreshTripReportStart: "Refresh trip report start",
    refreshTripReportError: "Refresh trip report exception",
    uploadWaveStart: "Wave file upload start",
    uploadWaveError: "Wave file upload exception"
  },
  configureService: {
    getConfigureListError: "Get configuration list exception",
    loadConfigureError: "Load configuration exception"
  },
  // Controller logs
  configureController: {
    initialized: "Controller initialized",
    getConfigureListStart: "Start getting configuration list",
    getConfigureListError: "Error occurred while getting configuration list",
    addConfigureStart: "Start adding configuration",
    addConfigureError: "Error occurred while adding configuration",
    renameConfigureStart: "Start renaming configuration",
    renameConfigureError: "Error occurred while renaming configuration",
    removeConfigureStart: "Start removing configuration",
    removeConfigureError: "Error occurred while removing configuration",
    saveConfigureStart: "Start saving configuration",
    saveConfigureError: "Error occurred while saving configuration",
    loadConfigureStart: "Start loading configuration",
    loadConfigureError: "Error occurred while loading configuration",
    openConfigureDirStart: "Start opening configuration directory",
    openConfigureDirError: "Error occurred while opening configuration directory"
  },
  deviceConnectController: {
    initialized: "Controller initialized",
    connectDeviceStart: "Start connecting device, connection parameters",
    connectDeviceCallService: "Call service layer to connect device",
    connectDeviceServiceResult: "Service layer return result",
    connectDeviceSuccess: "Device connection successful",
    connectDeviceGetError: "Get error information, log output",
    connectDeviceFailed: "Device connection failed, error information",
    connectDeviceException: "Catch exception, log output",
    connectDeviceExceptionDetail: "Device connection exception",
    disconnectDeviceStart: "Start disconnecting device, device ID",
    disconnectDeviceCheckStatus: "Check device connection status",
    disconnectDeviceAlready: "Device already disconnected, device ID",
    disconnectDeviceCallService: "Call service layer to disconnect device",
    disconnectDeviceResult: "Disconnect result",
    disconnectDeviceSuccess: "Device disconnection successful, device ID",
    disconnectDeviceException: "Device disconnection exception, device ID",
    disconnectDeviceFailed: "Device disconnection failed, device ID"
  },
  deviceOperateController: {
    initialized: "Controller initialized",
    addDeviceStart: "Start adding device configuration, request parameters",
    addDeviceCallService: "Call service layer to add device configuration",
    addDeviceSuccess: "Add device configuration successful, result",
    addDeviceException: "Add device configuration exception",
    updateDeviceStart: "Start updating device configuration, request parameters",
    updateDeviceCallService: "Call service layer to update device configuration",
    updateDeviceSuccess: "Update device configuration successful, result",
    updateDeviceException: "Update device configuration exception",
    removeDeviceStart: "Start removing device configuration, request parameters",
    removeDeviceCallService: "Call service layer to remove device configuration, device ID",
    removeDeviceResult: "Remove device configuration result",
    removeDeviceSuccess: "Remove device configuration successful, device ID",
    removeDeviceFailed: "Remove device configuration failed, device ID",
    removeDeviceException: "Remove device configuration exception, device ID",
    getDeviceListStart: "Start getting device configuration list",
    getDeviceListCallService: "Call service layer to get device configuration list",
    getDeviceListSuccess: "Successfully get device configuration list, device count",
    getDeviceListException: "Get device configuration list exception"
  },
  paramService: {
    getDiffParamComplete: "Comparison complete, difference groups",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo entry parameters",
    getParamInfoReturn: "getParamInfo return",
    getParamInfoError: "getParamInfo exception",
    startGetParamInfo: "Start getting parameter settings, pagination",
    getAllParamInfoStart: "Start getting all parameter settings",
    getAllParamInfoSuccess: "Successfully got all parameter settings, total",
    modifyParamStart: "Start modifying parameter settings",
    validateParam: "Validate parameter item",
    validateFailed: "Parameter validation failed",
    validatePassed: "Parameter validation passed, ready to send",
    setTimeout: "Set timeout",
    sendResponse: "Send response",
    modifySuccess: "Modification successful",
    sendFailed: "Send failed",
    businessError: "Business error",
    getAllDiffParamStart: "Start batch parameter comparison",
    excelParseFailed: "Excel parsing failed",
    csvParseFailed: "CSV parsing failed",
    xmlParseFailed: "XML parsing failed",
    fileParseComplete: "File parsing complete",
    getDiffParamStart: "Start single group parameter comparison",
    diffComplete: "Comparison complete, difference items",
    importParamStart: "Start importing parameter settings",
    paramReady: "Parameters ready to send",
    importSuccess: "Import successful",
    exportAllParamStart: "Start exporting all parameter settings",
    exportComplete: "Export complete",
    exportParamStart: "Start exporting group parameters",
    getGroupItemsStart: "Get group parameter items",
    getParamValueFailed: "Failed to get parameter value",
    getGroupItemsComplete: "Get complete, parameter items",
    getAllGroupItemsStart: "Get all group parameter items",
    groupParamCount: "Group: {group}, parameter items: {count}",
    getCurrentRunAreaStart: "Get current run area",
    getCurrentRunAreaSuccess: "Get successful",
    getCurrentRunAreaFailed: "Get failed",
    selectRunAreaStart: "Select setting area",
    runAreaEmpty: "Setting area cannot be empty",
    selectRunAreaSuccess: "Select successful",
    selectRunAreaFailed: "Select failed"
  },
  debugInfoMenuService: {
    initialized: "Initialization complete",
    getDebugInfoEntry: "getDebugInfo entry parameters",
    getTreeMenuError: "getTreeMenu exception",
    getTreeMenuComplete: "Processing complete, menu count"
  },
  deviceInfoController: {
    initialized: "Controller initialized",
    getDeviceInfoStart: "Start getting device information, request parameters",
    getDeviceInfoCheckConnection: "Check device connection status",
    getDeviceInfoNotConnected: "Device not connected, cannot get device information",
    getDeviceInfoConnected: "Device connected, call service layer to get device information",
    getDeviceInfoSuccess: "Successfully get device information, result count",
    getDeviceInfoException: "Get device information exception",
    exportDeviceInfoStart: "Start exporting device information, request parameters",
    exportDeviceInfoCheckConnection: "Check device connection status",
    exportDeviceInfoNotConnected: "Device not connected, cannot export device information",
    exportDeviceInfoValidateParams: "Validate export parameters, data count",
    exportDeviceInfoEmptyData: "Export data is empty",
    exportDeviceInfoEmptyPath: "Export file path is empty",
    exportDeviceInfoFileExtension: "File extension",
    exportDeviceInfoUnsupportedFormat: "Unsupported file format",
    exportDeviceInfoDirPath: "Export directory path",
    exportDeviceInfoCreateDir: "Create export directory",
    exportDeviceInfoCreateDirFailed: "Create export directory failed",
    exportDeviceInfoCallService: "Call service layer to export device information",
    exportDeviceInfoSuccess: "Device information export successful, export path",
    exportDeviceInfoException: "Export device information exception"
  },
  variableController: {
    getVariableEntry: "getVariable entry parameters",
    getVariableReturn: "Get variable method return log",
    getVariableException: "Get variable method exception log",
    addVariableEntry: "addVariable entry parameters",
    addVariableReturn: "Add variable method return log",
    addVariableException: "Add variable method exception log",
    modifyVariableEntry: "modifyVariable entry parameters",
    modifyVariableReturn: "Modify variable method return log",
    modifyVariableException: "Modify variable method exception log",
    deleteVariableEntry: "deleteVariable entry parameters",
    deleteVariableReturn: "Delete variable method return log",
    deleteVariableException: "Delete variable method exception log",
    exportVariableEntry: "exportVariable entry parameters",
    exportVariableEmptyPath: "Export path cannot be empty",
    exportVariableReturn: "Export variable method return log",
    exportVariableException: "Export variable method exception log",
    importVariableEntry: "importVariable entry parameters",
    importVariableEmptyPath: "Import path cannot be empty",
    importVariableReturn: "Import variable method return log",
    importVariableException: "Import variable method exception log"
  },
  paramController: {
    initialized: "Controller initialized",
    getParamStart: "Start getting device parameters, request parameters",
    getParamNotConnected: "Device not connected, cannot get device parameters",
    getParamConnected: "Device connected, call service layer to get device parameters",
    getParamSuccess: "Successfully get device parameters, result count",
    getParamException: "Get device parameters exception",
    getAllParamStart: "Start getting all device parameters, request parameters",
    getAllParamNotConnected: "Device not connected, cannot get all device parameters",
    getAllParamConnected: "Device connected, call service layer to get all device parameters",
    getAllParamSuccess: "Successfully get all device parameters, result count",
    getAllParamException: "Get all device parameters exception",
    confirmParamStart: "Start modifying device parameters, request parameters",
    confirmParamNotConnected: "Device not connected, cannot modify device parameters",
    confirmParamConnected: "Device connected, call service layer to modify device parameters",
    confirmParamSuccess: "Modify device parameters successful, result",
    confirmParamException: "Modify device parameters exception",
    getDiffParamStart: "Start getting parameter differences, request parameters",
    getDiffParamNotConnected: "Device not connected, cannot get parameter differences",
    getDiffParamPath: "Import path",
    getDiffParamEmptyPath: "Import path is empty",
    getDiffParamConnected: "Device connected, call service layer to get parameter differences",
    getDiffParamSuccess: "Successfully get parameter differences, result",
    getDiffParamException: "Get parameter differences exception",
    getAllDiffParamStart: "Start getting all parameter differences, request parameters",
    getAllDiffParamNotConnected: "Device not connected, cannot get all parameter differences",
    getAllDiffParamPath: "Import path",
    getAllDiffParamEmptyPath: "Import path is empty",
    getAllDiffParamConnected: "Device connected, call service layer to get all parameter differences",
    getAllDiffParamSuccess: "Successfully get all parameter differences, result",
    getAllDiffParamException: "Get all parameter differences exception",
    importParamStart: "Start importing device parameters, request parameters",
    importParamNotConnected: "Device not connected, cannot import device parameters",
    importParamConnected: "Device connected, call service layer to import device parameters",
    importParamSuccess: "Import device parameters successful, result",
    importParamException: "Import device parameters exception",
    exportParamStart: "Start exporting device parameters, request parameters",
    exportParamNotConnected: "Device not connected, cannot export device parameters",
    exportParamPath: "Export path",
    exportParamEmptyPath: "Export path is empty",
    exportParamConnected: "Device connected, call service layer to export device parameters",
    exportParamSuccess: "Export device parameters successful, result",
    exportParamException: "Export device parameters exception",
    exportAllParamStart: "Start exporting all device parameters, request parameters",
    exportAllParamNotConnected: "Device not connected, cannot export all device parameters",
    exportAllParamPath: "Export path",
    exportAllParamEmptyPath: "Export path is empty",
    exportAllParamConnected: "Device connected, call service layer to export all device parameters",
    exportAllParamSuccess: "Export all device parameters successful, result",
    exportAllParamException: "Export all device parameters exception",
    getCurrentRunAreaStart: "Start getting current run area, request parameters",
    getCurrentRunAreaNotConnected: "Device not connected, cannot get current run area",
    getCurrentRunAreaConnected: "Device connected, call service layer to get current run area",
    getCurrentRunAreaSuccess: "Successfully get current run area, result",
    getCurrentRunAreaException: "Get current run area exception",
    selectRunAreaStart: "Start selecting parameter area, request parameters",
    selectRunAreaNotConnected: "Device not connected, cannot select parameter area",
    selectRunAreaConnected: "Device connected, call service layer to select parameter area",
    selectRunAreaSuccess: "Successfully select parameter area, result",
    selectRunAreaException: "Select parameter area exception"
  },
  remoteControlController: {
    ykSelectEntry: "Remote control select method entry log",
    ykSelectReturn: "Remote control select method return log",
    ykSelectException: "Remote control select method exception log"
  }
};
