"use strict";

import { ApiResponse } from "../../data/debug/apiResponse";
import { DebugDeviceInfo } from "../../interface/debug/debuginfo";
import { IECReq } from "../../interface/debug/request";
import { deviceOperateService } from "../../service/debug/deviceoperate";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";

/**
 * 装置新增，修改，删除等操作
 */
class DeviceOperateController {
  constructor() {
    logger.info(
      `[DeviceOperateController] ${t("logs.deviceOperateController.initialized")}`
    );
  }

  // 新增装置
  public async addDeviceCfg(data: DebugDeviceInfo): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperateController] ${t("logs.deviceOperateController.addDeviceStart")}:`,
      data
    );

    try {
      logger.debug(
        `[DeviceOperateController] ${t("logs.deviceOperateController.addDeviceCallService")}`
      );
      const res = await deviceOperateService.addDeviceCfg(data);

      logger.info(
        `[DeviceOperateController] ${t("logs.deviceOperateController.addDeviceSuccess")}:`,
        res
      );
      return res;
    } catch (error) {
      logger.error(
        `[DeviceOperateController] ${t("logs.deviceOperateController.addDeviceException")}:`,
        error
      );
      return new ApiResponse(100001, t("errors.addDeviceConfigFailed"), error);
    }
  }

  // 修改装置
  public async updateDeviceCfg(
    data: IECReq<DebugDeviceInfo>
  ): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperateController] ${t("logs.deviceOperateController.updateDeviceStart")}:`,
      data
    );

    try {
      logger.debug(
        `[DeviceOperateController] ${t("logs.deviceOperateController.updateDeviceCallService")}`
      );
      const res = await deviceOperateService.updateDeviceCfg(data.data);

      logger.info(
        `[DeviceOperateController] ${t("logs.deviceOperateController.updateDeviceSuccess")}:`,
        res
      );
      return res;
    } catch (error) {
      logger.error(
        `[DeviceOperateController] ${t("logs.deviceOperateController.updateDeviceException")}:`,
        error
      );
      return new ApiResponse(
        100001,
        t("errors.updateDeviceConfigFailed"),
        error
      );
    }
  }

  // 删除
  public async removeDeviceCfg(req: IECReq<undefined>): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperateController] ${t("logs.deviceOperateController.removeDeviceStart")}:`,
      req
    );

    try {
      logger.debug(
        `[DeviceOperateController] ${t("logs.deviceOperateController.removeDeviceCallService")}: ${req.head.id}`
      );
      const res = await deviceOperateService.removeDeviceCfg(req.head.id);

      logger.info(
        `[DeviceOperateController] ${t("logs.deviceOperateController.removeDeviceResult")}:`,
        res
      );

      if (res) {
        logger.info(
          `[DeviceOperateController] ${t("logs.deviceOperateController.removeDeviceSuccess")}: ${req.head.id}`
        );
        return new ApiResponse(0, t("errors.deleteSuccess"));
      }

      logger.warn(
        `[DeviceOperateController] ${t("logs.deviceOperateController.removeDeviceFailed")}: ${req.head.id}`
      );
      return new ApiResponse(100001, t("errors.deleteFailed"));
    } catch (error) {
      logger.error(
        `[DeviceOperateController] ${t("logs.deviceOperateController.removeDeviceException")}: ${req.head.id}`,
        error
      );
      return new ApiResponse(100001, t("errors.deleteFailed"), error);
    }
  }

  /**
   *
   * @returns 获取配置的连接信息集合
   */
  async getDeviceCfgList(): Promise<ApiResponse> {
    logger.info(
      `[DeviceOperateController] ${t("logs.deviceOperateController.getDeviceListStart")}`
    );

    try {
      logger.debug(
        `[DeviceOperateController] ${t("logs.deviceOperateController.getDeviceListCallService")}`
      );
      const devices = await deviceOperateService.getList();

      logger.info(
        `[DeviceOperateController] ${t("logs.deviceOperateController.getDeviceListSuccess")}: ${devices?.length || 0}`
      );
      return new ApiResponse(0, t("errors.getDeviceList"), devices);
    } catch (error) {
      logger.error(
        `[DeviceOperateController] ${t("logs.deviceOperateController.getDeviceListException")}:`,
        error
      );
      return new ApiResponse(
        100001,
        t("errors.getDeviceConfigListFailed"),
        error
      );
    }
  }
}

DeviceOperateController.toString = () => "[class DeviceOperateController]";
module.exports = DeviceOperateController;