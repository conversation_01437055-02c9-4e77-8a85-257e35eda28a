"use strict";

const { logger } = require("ee-core/log");

import { ErrorCode, myAuthMgr } from "license";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { t } from "../../data/i18n/i18n";

/**
 * LicenseCheck SERVICE
 * <AUTHOR>
 * @class
 */
class LicenseService {
  private authCache: { result: boolean; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  // 返回机器码
  async getMachineCode(): Promise<string> {
    logger.info("LicenseService getMachineCode");
    var machineCode = await myAuthMgr.getMachineCode();
    return machineCode;
  }
  // 检查校验码
  async checkAuth(): Promise<boolean> {
    logger.info("LicenseService checkAuth");

    // 检查缓存
    if (
      this.authCache &&
      Date.now() - this.authCache.timestamp < this.CACHE_DURATION
    ) {
      logger.info(
        `LicenseService checkAuth - ${t("services.license.cacheResult")}`
      );
      return this.authCache.result;
    }

    const startTime = Date.now();
    const SOFTWARE_NAME = IECCONSTANTS.PRODUCTNAME; // 软件名称

    try {
      var checkResult = await myAuthMgr.checkAuth(SOFTWARE_NAME);
      const duration = Date.now() - startTime;
      logger.info(
        `LicenseService checkAuth - ${t("services.license.verificationTime")}: ${duration}ms`
      );

      console.log(checkResult.errInfo);
      if (checkResult.errCode === ErrorCode.SUCCESS) {
        // 缓存成功结果
        this.authCache = { result: true, timestamp: Date.now() };
        return true;
      } else {
        console.log(checkResult.errInfo);
        // 获取业务错误信息
        throw new Error(String(checkResult?.errInfo));
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(
        `LicenseService checkAuth - ${t("services.license.verificationFailed")}: ${duration}ms`,
        error
      );
      throw error;
    }
  }

  // 检查校验码
  async activate(activaeCode: string): Promise<boolean> {
    logger.info("LicenseService activate");
    const SOFTWARE_NAME = IECCONSTANTS.PRODUCTNAME;
    var checkResult = await myAuthMgr.activate(SOFTWARE_NAME, activaeCode);
    console.log(checkResult.errInfo);
    if (checkResult.errCode === ErrorCode.SUCCESS) {
      return true;
    } else {
      console.log(checkResult.errInfo);
      // 获取业务错误信息
      throw new Error(String(checkResult?.errInfo));
    }
  }
}

LicenseService.toString = () => "[class LicenseService]";
const licenseService = new LicenseService();

export { LicenseService, licenseService };
