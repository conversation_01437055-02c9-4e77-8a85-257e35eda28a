/**
 * Error messages - English
 */
export default {
  success: "Success",
  deviceNotConnected: "Device not connected",
  invalidParam: "Invalid parameter",
  operateFailed: "Operation failed",
  noData: "No data",
  internalError: "Internal error",
  connectionExists: "Connection already exists",
  fileContentEmpty: "File content is empty",
  deviceNotConnectedOrDisconnected: "Device not connected or disconnected.",
  getServiceErrorInfo: "Failed to get corresponding error information",
  saveReportFileError: "Error saving report rpt file",
  getConfigureListError: "Error getting configuration list",
  loadConfigureError: "Error loading configuration",
  cancelUploadError: "Error canceling wave file upload",
  openWaveFileError: "Error opening wave file",
  getFileDataSuccess: "Successfully retrieved file data content!",
  getHistoryReportError: "Error getting history report",
  getSuccessful: "Retrieved successfully",
  errorHandledInCatch: "Error handled in catch",
  waveFileNotFound: "Wave file not found",
  waveFileSizeZero: "Wave file size is 0",
  uploadException: "Upload exception",
  uploadFinished: "Upload finished",
  saveReportXlsxError: "Error saving report xlsx file",
  invalidXmlStructure: "Invalid XML structure: missing configVersion",
  failedToGetTreeMenu: "Failed to get tree menu",
  missingHeaders: "Missing headers",
  excelParseFailed: "Excel parsing failed",
  paramModifyError: "Parameter modification error, error items are:",
  paramConfirmError: "Parameter confirmation error, reason:",
  errorReason: ", error reason:",
  invalidValue: "Invalid value",
  errorItem: "Error item:",
  description: ", description",
  excelFileParseError: "Excel file parsing failed:",
  csvFileParseError: "CSV file parsing failed:",
  xmlFileParseError: "XML file parsing failed:",
  connectionFailed: "Connection failed",
  connectionTimeout: "Connection timeout",
  dataLengthMismatch: "Device returned vkeys data length does not match request length",
  invalidFilePath: "Invalid file path. Please provide a valid .xlsx file path.",
  failedToCreateDirectory: "Failed to create directory",
  failedToExportData: "Failed to export data",
  worksheetNotFound: "The Excel file does not contain the specified worksheet.",
  noHeaders: "The Excel file does not contain any headers.",
  parseExcelUnknownError: "Failed to parse Excel file with an unknown error",
  errorParsingXml: "Error parsing XML",
  failedToUpdateXmlFile: "Failed to update XML file",
  // Controller error messages
  addDeviceConfigFailed: "Failed to add device configuration",
  updateDeviceConfigFailed: "Failed to update device configuration",
  deleteDeviceConfigFailed: "Failed to delete device configuration",
  deleteSuccess: "Delete successful",
  deleteFailed: "Delete failed",
  getDeviceList: "Get devices",
  getDeviceConfigListFailed: "Failed to get device configuration list",
  exportFilePathEmpty: "Export file path is empty",
  exportFileExtensionError: "Export file extension error",
  exportFolderCreateFailed: "Failed to create export folder",
  variableNameEmpty: "Variable name cannot be empty",
  exportPathEmpty: "Export path cannot be empty",
  importPathEmpty: "Import path cannot be empty",
  deviceAlreadyConnected: "Device already connected"
};
