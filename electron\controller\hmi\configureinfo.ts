import {ApiResponse} from "../../data/debug/apiResponse";
import {configureService} from "../../service/hmi/configureinfo";
import {
    ERROR_CODES,
    ERROR_MESSAGES,
    handleCustomResponse, handleErrorResponse
} from "../../data/debug/errorCodes";
import {logger} from "ee-core/log";
import {
    ConfigureInfo,
    LoadConfigureInfo,
    openConfigureInfo,
    SaveConfigureInfo
} from "../../interface/hmi/configureinfo";
import { t } from "../../data/i18n/i18n";

/**
 * 组态相关
 * @class
 */
class ConfigureController {
  /**
   * 获取组态列表
   * @param req
   */
  async getConfigureList(): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.getConfigureListStart")}`,
        null
      );
      const res = await configureService.getConfigureList();
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.getConfigureListError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 新增组态
   * @param req
   */
  async addConfigure(req: ConfigureInfo): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.addConfigureStart")}`,
        req
      );
      const res = await configureService.addConfigure(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.addConfigureError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 重命名组态
   * @param req
   */
  async renameConfigure(req: ConfigureInfo): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.renameConfigureStart")}`,
        req
      );
      const res = await configureService.renameConfigure(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.renameConfigureError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 删除组态
   * @param req
   */
  async removeConfigure(req: ConfigureInfo): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.removeConfigureStart")}`,
        req
      );
      const res = await configureService.removeConfigure(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.removeConfigureError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 保存组态
   * @param req
   */
  async saveConfigure(req: SaveConfigureInfo): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.saveConfigureStart")}`,
        req
      );
      const res = await configureService.saveConfigure(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.saveConfigureError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 加载组态
   * @param req
   */
  async loadConfigure(req: LoadConfigureInfo): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.loadConfigureStart")}`,
        req
      );
      const res = await configureService.loadConfigure(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.loadConfigureError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 打开组态文件夹
   * @param req
   */
  async openConfigureDir(req: openConfigureInfo): Promise<ApiResponse> {
    try {
      logger.info(
        `[ConfigureController] ${t("logs.configureController.openConfigureDirStart")}`,
        req
      );
      const res = await configureService.openConfigureDir(req);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        res
      );
    } catch (error) {
      logger.error(
        `[ConfigureController] ${t("logs.configureController.openConfigureDirError")}`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

ConfigureController.toString = () => '[class ConfigureController]';

export default ConfigureController;