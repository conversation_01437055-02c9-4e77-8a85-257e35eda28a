/**
 * 日志信息 - 中文
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList 入参",
    getCommonReportListReturn: "getCommonReportList 返回",
    getCommonReportListError: "getCommonReportList 异常",
    cancelUploadStart: "取消录波文件上招开始",
    cancelUploadError: "取消录波文件上招异常",
    openWaveFileStart: "打开录波文件开始",
    openWaveFileError: "打开录波文件异常"
  },
  configureService: {
    getConfigureListError: "获取组态列表异常",
    loadConfigureError: "加载组态异常"
  },
  paramService: {
    getDiffParamComplete: "比对完成，差异组数",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo 入参",
    getParamInfoReturn: "getParamInfo 返回",
    getParamInfoError: "getParamInfo 异常",
    startGetParamInfo: "开始获取参数定值，分页",
    getAllParamInfoStart: "开始获取所有参数定值",
    getAllParamInfoSuccess: "获取所有参数定值成功，总数",
    modifyParamStart: "开始修改参数定值",
    validateParam: "校验参数项",
    validateFailed: "参数校验失败",
    validatePassed: "参数校验通过，准备下发",
    setTimeout: "设置超时时间",
    sendResponse: "下发响应",
    modifySuccess: "修改成功",
    sendFailed: "下发失败",
    businessError: "业务错误",
    getAllDiffParamStart: "开始批量比对参数",
    excelParseFailed: "Excel解析失败",
    csvParseFailed: "CSV解析失败",
    xmlParseFailed: "XML解析失败",
    fileParseComplete: "文件解析完成",
    getDiffParamStart: "开始单组比对参数",
    diffComplete: "比对完成，差异项数",
    importParamStart: "开始导入参数定值",
    paramReady: "参数准备下发",
    importSuccess: "导入成功",
    exportAllParamStart: "开始导出所有参数定值",
    exportComplete: "导出完成",
    exportParamStart: "开始导出分组参数",
    getGroupItemsStart: "获取分组参数项",
    getParamValueFailed: "获取参数值失败",
    getGroupItemsComplete: "获取完成，参数项数",
    getAllGroupItemsStart: "获取所有分组参数项",
    groupParamCount: "分组：{group}，参数项数：{count}",
    getCurrentRunAreaStart: "获取当前运行区",
    getCurrentRunAreaSuccess: "获取成功",
    getCurrentRunAreaFailed: "获取失败",
    selectRunAreaStart: "选择定值区",
    runAreaEmpty: "定值区不能为空",
    selectRunAreaSuccess: "选择成功",
    selectRunAreaFailed: "选择失败"
  },
  debugInfoMenuService: {
    initialized: "初始化完成",
    getDebugInfoEntry: "getDebugInfo 入参",
    getTreeMenuError: "getTreeMenu 异常",
    getTreeMenuComplete: "处理完成，菜单数量"
  }
};
