/**
 * Messages d'erreur - Français
 */
export default {
  success: "Succès",
  deviceNotConnected: "Appareil non connecté",
  invalidParam: "Paramètre invalide",
  operateFailed: "Opération échouée",
  noData: "Aucune donnée",
  internalError: "Erreur interne",
  connectionExists: "La connexion existe déjà",
  fileContentEmpty: "Le contenu du fichier est vide",
  deviceNotConnectedOrDisconnected: "Appareil non connecté ou déconnecté.",
  getServiceErrorInfo: "Échec de l'obtention des informations d'erreur correspondantes",
  saveReportFileError: "Erreur lors de la sauvegarde du fichier de rapport rpt",
  getConfigureListError: "Erreur lors de l'obtention de la liste de configuration",
  loadConfigureError: "Erreur lors du chargement de la configuration",
  cancelUploadError: "Erreur lors de l'annulation du téléchargement du fichier d'onde",
  openWaveFileError: "Erreur lors de l'ouverture du fichier d'onde",
  getFileDataSuccess: "Contenu des données du fichier récupéré avec succès !",
  getHistoryReportError: "Erreur lors de l'obtention du rapport historique",
  getSuccessful: "Obtenu avec succès",
  errorHandledInCatch: "Erreur gérée dans catch",
  waveFileNotFound: "Fichier d'onde non trouvé",
  waveFileSizeZero: "La taille du fichier d'onde est 0",
  uploadException: "Exception de téléchargement",
  uploadFinished: "Téléchargement terminé",
  saveReportXlsxError: "Erreur lors de la sauvegarde du fichier de rapport xlsx",
  invalidXmlStructure: "Structure XML invalide : configVersion manquant",
  failedToGetTreeMenu: "Échec de l'obtention du menu arborescent",
  missingHeaders: "En-têtes manquants",
  excelParseFailed: "Échec de l'analyse Excel",
  paramModifyError: "Erreur de modification de paramètre, éléments d'erreur sont :",
  paramConfirmError: "Erreur de confirmation de paramètre, raison :",
  errorReason: ", raison de l'erreur :",
  invalidValue: "valeur invalide",
  errorItem: "élément d'erreur :",
  description: ", description",
  excelFileParseError: "Échec de l'analyse du fichier Excel :",
  csvFileParseError: "Échec de l'analyse du fichier CSV :",
  xmlFileParseError: "Échec de l'analyse du fichier XML :",
  connectionFailed: "Connexion échouée",
  connectionTimeout: "Délai de connexion dépassé",
  dataLengthMismatch: "La longueur des données vkeys renvoyées par l'appareil ne correspond pas à la longueur de la demande",
  invalidFilePath: "Chemin de fichier invalide. Veuillez fournir un chemin de fichier .xlsx valide.",
  failedToCreateDirectory: "Échec de la création du répertoire",
  failedToExportData: "Échec de l'exportation des données",
  worksheetNotFound: "Le fichier Excel ne contient pas la feuille de calcul spécifiée.",
  noHeaders: "Le fichier Excel ne contient aucun en-tête.",
  parseExcelUnknownError: "Échec de l'analyse du fichier Excel avec une erreur inconnue",
  errorParsingXml: "Erreur lors de l'analyse XML",
  failedToUpdateXmlFile: "Échec de la mise à jour du fichier XML",
  // Messages d'erreur du contrôleur
  addDeviceConfigFailed: "Échec de l'ajout de la configuration du dispositif",
  updateDeviceConfigFailed: "Échec de la mise à jour de la configuration du dispositif",
  deleteDeviceConfigFailed: "Échec de la suppression de la configuration du dispositif",
  deleteSuccess: "Suppression réussie",
  deleteFailed: "Suppression échouée",
  getDeviceList: "Obtenir les dispositifs",
  getDeviceConfigListFailed: "Échec de l'obtention de la liste de configuration du dispositif",
  exportFilePathEmpty: "Le chemin du fichier d'exportation est vide",
  exportFileExtensionError: "Erreur d'extension du fichier d'exportation",
  exportFolderCreateFailed: "Échec de la création du dossier d'exportation",
  variableNameEmpty: "Le nom de la variable ne peut pas être vide",
  exportPathEmpty: "Le chemin d'exportation ne peut pas être vide",
  importPathEmpty: "Le chemin d'importation ne peut pas être vide",
  deviceAlreadyConnected: "Dispositif déjà connecté"
};
