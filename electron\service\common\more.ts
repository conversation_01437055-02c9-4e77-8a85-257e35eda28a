"use strict";
import IECCONSTANTS from "../../data/debug/iecConstants";
import {ImportConfigParam, ExportConfigParam, ConfigTypeEnum} from "../../interface/common/more";
import {copyFolder,deleteFolderRecursive} from "../../utils/common";
import { t } from "../../data/i18n/i18n";

const fs = require("fs");
const path = require("path");

class MoreService {
  // 导入工程配置
  async importConfig(req: ImportConfigParam): Promise<boolean> {
    const { sourcePath, type } = req;
    if (!fs.existsSync(sourcePath)) {
      throw new Error(t("services.more.importPathNotExists"));
    }
    if (!checkImport(sourcePath, type)) {
      throw new Error(t("services.more.selectCorrectConfigFile"));
    }
    try {
      if (fs.statSync(sourcePath).isDirectory()) {
        let targetPath = IECCONSTANTS.PATH_MAIN;
        if (type == ConfigTypeEnum.TYPE_CONFIGURE) {
          targetPath = IECCONSTANTS.PATH_CONFIGURE;
        }
        deleteFolderRecursive(targetPath);
        copyFolder(sourcePath, targetPath);
        return true;
      }
      // 读取源文件内容
      const configContent = fs.readFileSync(sourcePath, "utf-8");
      // 写入目标目录
      let target = "";
      if (type == ConfigTypeEnum.TYPE_DEVICE) {
        target = IECCONSTANTS.PATH_CONFIG;
      }
      if (type == ConfigTypeEnum.TYPE_CONFIGURE) {
        target = IECCONSTANTS.PATH_CONFIGURE;
      }
      const targetPath = path.join(target, path.basename(sourcePath));
      fs.writeFileSync(targetPath, configContent);
      return true;
    } catch (error) {
      console.error(
        `[MoreService] ${t("services.more.importProjectConfigException")}:`,
        error
      );
      throw error;
    }
  }

  // 导出工程配置
  async exportConfig(req: ExportConfigParam): Promise<boolean> {
    const { targetPath, type } = req;
    let sourceDir = IECCONSTANTS.PATH_MAIN;
    let targetDir = targetPath;
    if (!fs.existsSync(targetPath)) {
      throw new Error(t("services.more.exportPathNotExists"));
    }
    if (type == ConfigTypeEnum.TYPE_DEVICE) {
      sourceDir = path.join(IECCONSTANTS.PATH_CONFIG, "/deviceCfg.json");
      targetDir = path.join(targetDir, "/deviceCfg.json");
    }
    if (type == ConfigTypeEnum.TYPE_CONFIGURE) {
      sourceDir = path.join(sourceDir, "/hmi");
      targetDir = path.join(targetDir, "/hmi");
    }

    try {
      if (fs.statSync(sourceDir).isFile()) {
        // 复制文件
        fs.copyFileSync(sourceDir, targetDir);
      } else {
        if (type == ConfigTypeEnum.TYPE_ALL) {
          targetDir = path.join(targetPath, "/.config");
        }
        // 读取源目录中的所有配置文件
        copyFolder(sourceDir, targetDir);
      }
      return true;
    } catch (error) {
      console.error(
        `[MoreService] ${t("services.more.exportProjectConfigException")}:`,
        error
      );
      throw error;
    }
  }
}

const checkImport = (sourcePath, type): boolean => {
    let fileName = "";
    switch (type) {
        case ConfigTypeEnum.TYPE_ALL:
            fileName = ".config";
            break;
        case ConfigTypeEnum.TYPE_DEVICE:
            fileName = "deviceCfg.json";
            break;
        case ConfigTypeEnum.TYPE_CONFIGURE:
            fileName = "hmi";
            break;
    }
    return sourcePath.includes(fileName);
}

MoreService.toString = () => "[class MoreService]";
const moreService = new MoreService();

export {MoreService, moreService};
