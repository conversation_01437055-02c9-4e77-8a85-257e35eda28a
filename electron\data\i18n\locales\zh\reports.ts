/**
 * 报告相关 - 中文
 */
export default {
  service: "报告相关Service",
  description: "负责历史报告、操作报告、故障报告的查询、导出、清除等业务逻辑",
  getCommonReport: "获取通用报告",
  getCommonReportEntry: "获取通用报告方法入口日志",
  getGroupReport: "获取整组报告",
  getOperateReport: "获取操作报告",
  getAuditReport: "获取审计报告",
  exportCommonReport: "导出通用报告",
  clearReport: "清除报告",
  refreshReport: "刷新报告",
  uploadWave: "录波文件上招",
  cancelUpload: "取消录波文件上招",
  openWaveFile: "打开录波文件",
  getOperateReportEnd: "获取操作报告结束",
  getAuditReportEnd: "获取审计报告结束",
  workbookName: "report",
  exportContent: "导出内容的字段",
  headers: {
    reportId: "报告编号",
    reportTime: "报告时间",
    description: "描述",
    name: "名称",
    time: "时间",
    operateAddress: "操作地址",
    operateParam: "操作参数",
    value: "值",
    step: "步骤",
    source: "源",
    sourceType: "源类型",
    result: "结果",
    module: "模块",
    message: "消息",
    type: "类型",
    level: "级别",
    origin: "来源",
    user: "用户"
  }
};
