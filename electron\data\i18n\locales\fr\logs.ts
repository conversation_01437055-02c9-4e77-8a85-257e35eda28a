/**
 * Messages de journal - Français
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList paramètres d'entrée",
    getCommonReportListReturn: "getCommonReportList retour",
    getCommonReportListError: "getCommonReportList exception",
    cancelUploadStart: "Annuler le téléchargement du fichier d'onde commencé",
    cancelUploadError: "Annuler le téléchargement du fichier d'onde exception",
    openWaveFileStart: "Ouvrir le fichier d'onde commencé",
    openWaveFileError: "Ouvrir le fichier d'onde exception"
  },
  configureService: {
    getConfigureListError: "Erreur lors de l'obtention de la liste de configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration"
  },
  paramService: {
    getDiffParamComplete: "Comparaison terminée, groupes de différence",
    getAllDiffParamError: "getAllDiffParam erreur",
    getParamInfoEntry: "getParamInfo paramètres d'entrée",
    getParamInfoReturn: "getParamInfo retour",
    getParamInfoError: "getParamInfo exception",
    startGetParamInfo: "Commencer à obtenir les paramètres, pagination",
    getAllParamInfoStart: "Commencer à obtenir tous les paramètres",
    getAllParamInfoSuccess: "Obtenu avec succès tous les paramètres, total",
    modifyParamStart: "Commencer à modifier les paramètres",
    validateParam: "Valider l'élément de paramètre",
    validateFailed: "Validation du paramètre échouée",
    validatePassed: "Validation du paramètre réussie, prêt à envoyer",
    setTimeout: "Définir le délai d'attente",
    sendResponse: "Envoyer la réponse",
    modifySuccess: "Modification réussie",
    sendFailed: "Envoi échoué",
    businessError: "Erreur métier",
    getAllDiffParamStart: "Commencer la comparaison de paramètres par lots",
    excelParseFailed: "Analyse Excel échouée",
    csvParseFailed: "Analyse CSV échouée",
    xmlParseFailed: "Analyse XML échouée",
    fileParseComplete: "Analyse de fichier terminée",
    getDiffParamStart: "Commencer la comparaison de paramètres de groupe unique",
    diffComplete: "Comparaison terminée, éléments de différence",
    importParamStart: "Commencer l'importation des paramètres",
    paramReady: "Paramètres prêts à envoyer",
    importSuccess: "Importation réussie",
    exportAllParamStart: "Commencer l'exportation de tous les paramètres",
    exportComplete: "Exportation terminée",
    exportParamStart: "Commencer l'exportation des paramètres de groupe",
    getGroupItemsStart: "Obtenir les éléments de paramètres de groupe",
    getParamValueFailed: "Échec de l'obtention de la valeur du paramètre",
    getGroupItemsComplete: "Obtention terminée, éléments de paramètres",
    getAllGroupItemsStart: "Obtenir tous les éléments de paramètres de groupe",
    groupParamCount: "Groupe : {group}, éléments de paramètres : {count}",
    getCurrentRunAreaStart: "Obtenir la zone d'exécution actuelle",
    getCurrentRunAreaSuccess: "Obtention réussie",
    getCurrentRunAreaFailed: "Obtention échouée",
    selectRunAreaStart: "Sélectionner la zone de paramétrage",
    runAreaEmpty: "La zone de paramétrage ne peut pas être vide",
    selectRunAreaSuccess: "Sélection réussie",
    selectRunAreaFailed: "Sélection échouée"
  },
  debugInfoMenuService: {
    initialized: "Initialisation terminée",
    getDebugInfoEntry: "getDebugInfo paramètres d'entrée",
    getTreeMenuError: "getTreeMenu exception",
    getTreeMenuComplete: "Traitement terminé, nombre de menus"
  }
};
