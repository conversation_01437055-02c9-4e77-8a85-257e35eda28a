/**
 * License验证优化配置
 */

export interface LicenseConfig {
  // 缓存配置
  enableCache: boolean;
  cacheDuration: number; // 缓存持续时间（毫秒）
  
  // 超时配置
  checkTimeout: number; // 验证超时时间（毫秒）
  
  // 重试配置
  enableRetry: boolean;
  maxRetries: number;
  retryDelay: number; // 重试延迟（毫秒）
  
  // 性能监控
  enablePerformanceMonitor: boolean;
  
  // 异步处理
  enableAsyncProcessing: boolean;
}

// 默认配置
export const defaultLicenseConfig: LicenseConfig = {
  // 缓存配置
  enableCache: true,
  cacheDuration: 5 * 60 * 1000, // 5分钟
  
  // 超时配置
  checkTimeout: 30 * 1000, // 30秒
  
  // 重试配置
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  
  // 性能监控
  enablePerformanceMonitor: true,
  
  // 异步处理
  enableAsyncProcessing: true
};

// 获取License配置
export function getLicenseConfig(): LicenseConfig {
  // 可以从环境变量或配置文件读取
  return {
    ...defaultLicenseConfig,
    // 生产环境可能需要不同的配置
    ...(process.env.NODE_ENV === 'production' ? {
      cacheDuration: 10 * 60 * 1000, // 生产环境缓存10分钟
      checkTimeout: 60 * 1000, // 生产环境超时60秒
    } : {})
  };
}

// 性能优化建议
export const performanceOptimizations = {
  // 机器码生成优化
  machineCodeOptimization: {
    description: "优化机器码生成过程",
    suggestions: [
      "缓存CPU、硬盘、主板序列号",
      "使用异步并行获取硬件信息",
      "优化字符串拼接和加密算法"
    ]
  },
  
  // 网络时间获取优化
  networkTimeOptimization: {
    description: "优化网络时间获取",
    suggestions: [
      "使用本地时间作为备选方案",
      "设置合理的网络超时时间",
      "缓存网络时间结果"
    ]
  },
  
  // 授权验证优化
  authVerificationOptimization: {
    description: "优化授权验证流程",
    suggestions: [
      "实现验证结果缓存",
      "使用异步验证避免阻塞",
      "添加验证超时和重试机制"
    ]
  }
};
