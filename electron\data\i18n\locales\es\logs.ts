/**
 * Mensajes de registro - Español
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList parámetros de entrada",
    getCommonReportListReturn: "getCommonReportList retorno",
    getCommonReportListError: "getCommonReportList excepción",
    cancelUploadStart: "Cancelar carga de archivo de onda iniciado",
    cancelUploadError: "Cancelar carga de archivo de onda excepción",
    openWaveFileStart: "Abrir archivo de onda iniciado",
    openWaveFileError: "Abrir archivo de onda excepción"
  },
  configureService: {
    getConfigureListError: "Error al obtener lista de configuración",
    loadConfigureError: "Error al cargar configuración"
  },
  paramService: {
    getDiffParamComplete: "Comparación completada, grupos de diferencia",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo parámetros de entrada",
    getParamInfoReturn: "getParamInfo retorno",
    getParamInfoError: "getParamInfo excepción",
    startGetParamInfo: "Comenzar a obtener configuraciones de parámetros, paginación",
    getAllParamInfoStart: "Comenzar a obtener todas las configuraciones de parámetros",
    getAllParamInfoSuccess: "Obtuvo con éxito todas las configuraciones de parámetros, total",
    modifyParamStart: "Comenzar a modificar configuraciones de parámetros",
    validateParam: "Validar elemento de parámetro",
    validateFailed: "Validación de parámetro fallida",
    validatePassed: "Validación de parámetro pasada, listo para enviar",
    setTimeout: "Establecer tiempo de espera",
    sendResponse: "Enviar respuesta",
    modifySuccess: "Modificación exitosa",
    sendFailed: "Envío fallido",
    businessError: "Error de negocio",
    getAllDiffParamStart: "Comenzar comparación de parámetros por lotes",
    excelParseFailed: "Análisis de Excel fallido",
    csvParseFailed: "Análisis de CSV fallido",
    xmlParseFailed: "Análisis de XML fallido",
    fileParseComplete: "Análisis de archivo completado",
    getDiffParamStart: "Comenzar comparación de parámetros de grupo único",
    diffComplete: "Comparación completada, elementos de diferencia",
    importParamStart: "Comenzar importación de configuraciones de parámetros",
    paramReady: "Parámetros listos para enviar",
    importSuccess: "Importación exitosa",
    exportAllParamStart: "Comenzar exportación de todas las configuraciones de parámetros",
    exportComplete: "Exportación completada",
    exportParamStart: "Comenzar exportación de parámetros de grupo",
    getGroupItemsStart: "Obtener elementos de parámetros de grupo",
    getParamValueFailed: "Error al obtener valor de parámetro",
    getGroupItemsComplete: "Obtención completada, elementos de parámetros",
    getAllGroupItemsStart: "Obtener todos los elementos de parámetros de grupo",
    groupParamCount: "Grupo: {group}, elementos de parámetros: {count}",
    getCurrentRunAreaStart: "Obtener área de ejecución actual",
    getCurrentRunAreaSuccess: "Obtención exitosa",
    getCurrentRunAreaFailed: "Obtención fallida",
    selectRunAreaStart: "Seleccionar área de configuración",
    runAreaEmpty: "El área de configuración no puede estar vacía",
    selectRunAreaSuccess: "Selección exitosa",
    selectRunAreaFailed: "Selección fallida"
  },
  debugInfoMenuService: {
    initialized: "Inicialización completada",
    getDebugInfoEntry: "getDebugInfo parámetros de entrada",
    getTreeMenuError: "getTreeMenu excepción",
    getTreeMenuComplete: "Procesamiento completado, cantidad de menús"
  }
};
