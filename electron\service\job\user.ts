import { logger } from 'ee-core/log';
import { t } from "../../data/i18n/i18n";

/**
 * UserService class
 */
class UserService {

  async hello(args: any): Promise<{ status: string; params: any }> {
    const obj = {
      status: t("common.success"),
      params: args,
    };
    logger.info('UserService obj:', obj);
    return obj;
  }
}
UserService.toString = () => '[class UserService]';

export { UserService };