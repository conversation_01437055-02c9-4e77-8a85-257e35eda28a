/**
 * 服务相关 - 中文
 */
export default {
  // 许可证服务
  license: {
    cacheResult: "使用缓存结果",
    verificationTime: "验证耗时",
    verificationFailed: "验证失败，耗时",
    businessErrorInfo: "获取业务错误信息"
  },

  // 跨平台服务
  cross: {
    startGoService: "开始启动Go服务...",
    goServiceStartSuccess: "Go服务启动成功，耗时",
    goServiceStartFailed: "Go服务启动失败，耗时",
    startPythonService: "开始启动Python服务...",
    pythonServiceStartSuccess: "Python服务启动成功，耗时",
    pythonServiceStartFailed: "Python服务启动失败，耗时",
    optimizeStartParams: "优化启动参数"
  },

  // 基础服务
  base: {
    getClientStart: "开始获取设备客户端，设备ID",
    deviceNotFound: "未找到设备信息，设备ID",
    deviceNotConnected: "设备未连接或客户端无效，设备ID",
    getClientSuccess: "成功获取设备客户端，设备ID"
  },

  // 设备连接服务
  deviceConnect: {
    deviceConnected: "设备已连接",
    connectionAttempt: "连接尝试"
  },

  // 设备信息服务
  deviceInfo: {
    getDeviceInfoStart: "开始获取设备信息",
    exportStart: "开始导出设备信息",
    exportSuccess: "导出设备信息成功"
  },

  // 组态服务
  configure: {
    getConfigureList: "获取组态列表",
    addConfigure: "添加组态",
    setId: "设置ID",
    projectNotExists: "项目不存在",
    duplicateName: "名称重复，请重新输入",
    addConfigureException: "添加组态异常",
    projectNotFound: "未找到项目",
    projectPathNotFound: "未找到项目路径",
    replaceWithNew: "替换为新内容",
    projectNotFoundShort: "项目未找到",
    operationTypeIncorrect: "操作类型不正确，允许的值为[project,hmi]",
    renameConfigureException: "重命名组态异常",
    getConfigureListException: "获取组态列表异常",
    configureSaveException: "组态保存异常",
    openConfigureFolder: "打开组态文件夹"
  }
};
