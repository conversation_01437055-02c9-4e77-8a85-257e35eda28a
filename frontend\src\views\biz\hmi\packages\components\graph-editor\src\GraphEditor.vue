<template>
  <div class="header">
    <div class="toolbar">
      <div :title="t('hmi.graph.toolbar.undo')" class="toolbar-item" @click="onToolClick('undo')">
        <hmi-icon-undo :width="32" :height="32" />
      </div>
      <div :title="t('hmi.graph.toolbar.redo')" class="toolbar-item" @click="onToolClick('redo')">
        <hmi-icon-redo :width="32" :height="28" />
      </div>
      <el-divider direction="vertical"></el-divider>
      <div :title="t('hmi.graph.toolbar.bringToFront')" class="toolbar-item" @click="onToolClick('front')">
        <hmi-icon-front :width="32" :height="18" />
      </div>
      <div :title="t('hmi.graph.toolbar.sendToBack')" class="toolbar-item" @click="onToolClick('back')">
        <hmi-icon-back :width="32" :height="18" />
      </div>
      <el-divider direction="vertical"></el-divider>
      <!--
      <div :title="t('hmi.graph.toolbar.ratio')" class="toolbar-item" @click="onToolClick('ratio')">
        <hmi-icon-ratio :width="32" :height="26" />
      </div>
      -->
      <div :title="t('hmi.graph.toolbar.delete')" class="toolbar-item" @click="onToolClick('delete')">
        <hmi-icon-delete :width="32" :height="26" />
      </div>
      <el-divider direction="vertical"></el-divider>
      <div :title="t('hmi.graph.toolbar.save')" class="toolbar-item" @click="onSave">
        <hmi-icon-save :width="32" :height="20" />
      </div>
    </div>
  </div>
  <div class="layout">
    <div id="left" class="left">
      <GraphComponent :equipment-datas="prop.data.equipmentDatas" :electric-datas="form.electricDatas" @add-component="addComponent"></GraphComponent>
    </div>
    <div id="content" class="container">
      <div style="width: 100%; height: 100%">
        <div id="canvas" ref="canvas"></div>
      </div>
    </div>
    <div id="right" class="right">
      <GraphProperties></GraphProperties>
    </div>
  </div>
  <el-dialog v-model="form.saddrShow" :close-on-click-modal="false" :destroy-on-close="true" :title="t('hmi.graph.dialog.dataConfig')" width="50%">
    <SetSAddr :value="form.saddrData" @get-equipment-list="onEquipmentList" @end="onSetSaddrEnd"></SetSAddr>
  </el-dialog>
</template>
<script lang="ts">
export default {
  name: "GraphEditor"
};
</script>
<script setup lang="ts">
import HmiIconUndo from "../../graph-icons/undo.vue";
import HmiIconRedo from "../../graph-icons/redo.vue";
import HmiIconFront from "../../graph-icons/front.vue";
import HmiIconBack from "../../graph-icons/back.vue";
import HmiIconSave from "../../graph-icons/save.vue";
//import HmiIconRatio from "../../graph-icons/ratio.vue";
import HmiIconDelete from "../../graph-icons/delete.vue";

import Split from "split.js";
import GraphComponent from "./GraphComponent.vue";
import { GraphProperties } from "../../../graph-properties";
import { SetSAddr } from "../../../graph/saddr";
import { Cell, Graph, Model, Node } from "@antv/x6";
import { onMounted, ref } from "vue";
import GraphUsePlugin from "../../../graph/GraphUsePlugin";
import GraphEvent from "../../../graph/GraphEvent";
import { Dnd } from "@antv/x6-plugin-dnd";
import {
  ComponentInfo,
  ContextMenuItem,
  ContextMenuItemType,
  DataInfo,
  EquipmentConfig,
  EquipmentData,
  EquipmentManagerName,
  EventType,
  EventTypeParams,
  EventTypeParamsName,
  GraphJsonData
} from "../../../graph/Graph";
import { provide } from "vue";
import EquipmentManager from "../../../equipment/EquipmentManager";
import { equipmentDataToCell, getGraphJsonData, getSelectedCells, initGraphData, setCellDataEquipmentData } from "../../../graph/GraphUtil";
import { ElMessageBox } from "element-plus";
import GrapgGroup from "../../../graph/GraphGroup";
import GraphContextMenuManager from "./GraphContextMenuManager";
import GraphOperator from "../../../graph/GraphOperator";
import { useI18n } from "vue-i18n";

let graph: Graph;
let dnd: Dnd;
const graphOperator = new GraphOperator();
interface GraphEditorData {
  /** 画图数据 */
  graphData?: Model.FromJSONData;
  equipmentDatas: EquipmentData[];
}
// 定义属性
const prop = defineProps<{
  data: GraphEditorData;
}>();
// 定义事件
const emit = defineEmits<{
  (e: "getEquipmentList"): void;
  (e: "save", data: GraphJsonData): void;
}>();
// 定义方法
defineExpose({
  setGraphData: (data: GraphJsonData) => {
    initGraphData(graph, data);
  },
  setEquipmentList: (datas: EquipmentData[]) => {
    setEquipmentList(datas);
  }
});
const canvas = ref();
// 表单数据
const form = ref<{
  electricDatas: EquipmentData[];
  saddrShow: boolean;
  saddrData: {
    cell: unknown;
    equipmentShow: boolean;
    equipmentList: EquipmentData[];
  };
}>({
  electricDatas: [],
  saddrShow: false,
  saddrData: { cell: undefined, equipmentShow: false, equipmentList: [] }
});
// 注册提供者
const graphPropertiesProvide = ref<EventTypeParams>({
  type: EventType.NONE,
  eventParam: {}
});
provide(EventTypeParamsName, graphPropertiesProvide);
// 电气符号
const equipmentManager = new EquipmentManager();

provide(EquipmentManagerName, equipmentManager);
// 右键
const contextMenuManager = new GraphContextMenuManager();

const { t } = useI18n();

const registerElectricNode = async () => {
  try {
    await equipmentManager.loadEquipment();
    equipmentManager.registerNode();
  } catch (e) {
    console.log(t("hmi.graph.message.loadEquipmentFailed"));
  } finally {
    form.value.electricDatas = equipmentManager.equipmentList;
  }
};
onMounted(async () => {
  // 加载图符
  await registerElectricNode();
  Split(["#left", "#content", "#right"], {
    gutterSize: 6,
    sizes: [15, 80, 15]
  });
  graph = new Graph({
    container: canvas.value as HTMLElement,
    grid: {
      visible: true,
      size: 10,
      type: "mesh"
    },
    background: {
      color: "#fff"
    },
    translating: {
      restrict: true
    },
    panning: false,
    autoResize: true
  });
  // 加载插件
  const dataInfo: DataInfo = {
    operator: graphOperator,
    group: new GrapgGroup(graph)
  };
  const grapgUsePlugin = new GraphUsePlugin(graph);
  grapgUsePlugin.use(dataInfo);
  dnd = grapgUsePlugin.dnd;
  // 处理事件
  new GraphEvent(
    graph,
    {
      create: contextMenuManager.createContextMenu,
      trigger: contextMenuEvent
    },
    emitEvent
  );
  if (prop.data && prop.data.graphData) {
    graph.fromJSON(prop.data.graphData);
  }
});

const addComponent = (event: MouseEvent, componentInfo: ComponentInfo) => {
  if (!graph) {
    console.log(t("hmi.graph.message.waitForCanvasInit"));
    return;
  }
  let cells: Cell[];
  let node: Node | undefined = undefined;
  // 自定义图符的(不包括默认电气符号)
  if (componentInfo.equipmentData) {
    const allCells = [...equipmentDataToCell(componentInfo.equipmentData, graph)];
    cells = allCells[componentInfo.cellEquipmentData?.equipmentIndex as number];
    if (cells.length > 1) {
      // 组合的模拟一个Node
      node = graph.createNode({
        shape: "rect",
        width: 100,
        height: 50,
        data: {
          dndMock: true,
          dndCells: cells
        }
      });
    } else {
      node = cells[0] as Node;
    }
  } else {
    const data = componentInfo.data;
    node = graph.createNode(data);
    // 设置属性数据
    if (componentInfo.cellEquipmentData) {
      setCellDataEquipmentData(node, componentInfo.cellEquipmentData);
    }
  }
  if (node) {
    dnd.start(node, event);
  }
};

const emitEvent = (args: EventTypeParams) => {
  switch (args.type) {
    case EventType.BLANK_CLICK:
    case EventType.NODE_CLICK:
      graphPropertiesProvide.value = {
        ...args
      };
      break;
    default:
      break;
  }
};

const onToolClick = (type: string) => {
  switch (type) {
    case "undo":
      graph.undo();
      break;
    case "redo":
      graph.redo();
      break;
    case "front":
      graphOperator.toFront(graph);
      break;
    case "back":
      graphOperator.toBack(graph);
      break;
    case "delete":
      graphOperator.deleteSelectCells(graph);
      break;
    default:
      break;
  }
};
const onSave = () => {
  if (!graph) {
    return;
  }
  emit("save", getGraphJsonData(graph));
};
const contextMenuEvent = (item: ContextMenuItem, graph: Graph, graphGroup: GrapgGroup) => {
  switch (item.type) {
    case ContextMenuItemType.EQUIPMENT_SADDR:
      onSetSAddr();
      break;
    default:
      contextMenuManager.trigger(item, graph, graphGroup);
      break;
  }
};
// 触发get事件，查询设备信息
const onEquipmentList = () => {
  // 查询数据
  emit("getEquipmentList");
};
/**
 * 显示设备管理
 */
const setEquipmentList = (datas: EquipmentData[]) => {
  form.value.saddrData.equipmentList = datas;
  form.value.saddrData.equipmentShow = true;
};
const onSetSAddr = () => {
  const cells = getSelectedCells(graph);
  if (cells.length != 1) {
    ElMessageBox.alert(t("hmi.graph.dialog.selectOneGraph"), {
      title: t("hmi.graph.dialog.tip"),
      type: "warning"
    });
    return;
  }
  form.value.saddrData.cell = cells[0].value;
  form.value.saddrShow = true;
};
const onSetSaddrEnd = (data?: EquipmentConfig) => {
  if (data) {
    const cell = form.value.saddrData.cell as Cell;
    if (cell) {
      cell.setData({ equipmentConfig: data });
    }
  }
  form.value.saddrShow = false;
};
</script>
<style scoped lang="scss">
@import "../../../styles/Graph.css";
.layout {
  height: calc(100% - 40px);
  .left {
    height: 100%;
    overflow: auto;
  }
  .container {
    height: 100%;
    overflow: auto;
    .canvas {
      width: 100%;
      height: 100%;
    }
  }
  .right {
    height: 100%;
    overflow: auto;
  }
}
.gutter {
  background-color: #eeeeee;
  background-repeat: no-repeat;
  background-position: 50%;
}
.gutter.gutter-horizontal {
  cursor: col-resize;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==");
}

/** 文本编辑器定位问题 */
.x6-cell-tool-editor {
  position: absolute;
}
</style>
./GraphEvent
