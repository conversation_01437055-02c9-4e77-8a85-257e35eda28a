/**
 * Relatif aux rapports - Français
 */
export default {
  service: "Service relatif aux rapports",
  description: "Responsable de la consultation, exportation, suppression de la logique métier des rapports historiques, rapports d'opération, rapports de défaut",
  getCommonReport: "Obtenir rapport commun",
  getCommonReportEntry: "Journal d'entrée de méthode obtenir rapport commun",
  getGroupReport: "Obtenir rapport de groupe",
  getOperateReport: "Obtenir rapport d'opération",
  getAuditReport: "Obtenir rapport d'audit",
  exportCommonReport: "Exporter rapport commun",
  clearReport: "Effacer rapport",
  refreshReport: "Actualiser rapport",
  uploadWave: "Téléchargement de fichier d'onde",
  cancelUpload: "Annuler téléchargement de fichier d'onde",
  openWaveFile: "Ouvrir fichier d'onde",
  getOperateReportEnd: "Fin d'obtention de rapport d'opération",
  getAuditReportEnd: "Fin d'obtention de rapport d'audit",
  workbookName: "rapport",
  exportContent: "Champs de contenu d'exportation",
  headers: {
    reportId: "ID de Rapport",
    reportTime: "Heure de Rapport",
    description: "Description",
    name: "Nom",
    time: "Heure",
    operateAddress: "Adresse d'Opération",
    operateParam: "Paramètre d'Opération",
    value: "Valeur",
    step: "Étape",
    source: "Source",
    sourceType: "Type de Source",
    result: "Résultat",
    module: "Module",
    message: "Message",
    type: "Type",
    level: "Niveau",
    origin: "Origine",
    user: "Utilisateur"
  }
};
