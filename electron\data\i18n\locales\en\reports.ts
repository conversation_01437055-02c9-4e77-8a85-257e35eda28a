/**
 * Reports related - English
 */
export default {
  service: "Report related Service",
  description: "Responsible for querying, exporting, clearing business logic of historical reports, operation reports, fault reports",
  getCommonReport: "Get common report",
  getCommonReportEntry: "Get common report method entry log",
  getGroupReport: "Get group report",
  getOperateReport: "Get operation report",
  getAuditReport: "Get audit report",
  exportCommonReport: "Export common report",
  clearReport: "Clear report",
  refreshReport: "Refresh report",
  uploadWave: "Wave file upload",
  cancelUpload: "Cancel wave file upload",
  openWaveFile: "Open wave file",
  getOperateReportEnd: "Get operation report end",
  getAuditReportEnd: "Get audit report end",
  workbookName: "report",
  exportContent: "Export content fields",
  headers: {
    reportId: "Report ID",
    reportTime: "Report Time",
    description: "Description",
    name: "Name",
    time: "Time",
    operateAddress: "Operation Address",
    operateParam: "Operation Parameter",
    value: "Value",
    step: "Step",
    source: "Source",
    sourceType: "Source Type",
    result: "Result",
    module: "Module",
    message: "Message",
    type: "Type",
    level: "Level",
    origin: "Origin",
    user: "User"
  }
};
