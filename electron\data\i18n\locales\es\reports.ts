/**
 * Relacionado con informes - Español
 */
export default {
  service: "Servicio relacionado con informes",
  description: "Responsable de consultar, exportar, limpiar lógica de negocio de informes históricos, informes de operación, informes de fallas",
  getCommonReport: "Obtener informe común",
  getCommonReportEntry: "Registro de entrada del método obtener informe común",
  getGroupReport: "Obtener informe de grupo",
  getOperateReport: "Obtener informe de operación",
  getAuditReport: "Obtener informe de auditoría",
  exportCommonReport: "Exportar informe común",
  clearReport: "Limpiar informe",
  refreshReport: "Actualizar informe",
  uploadWave: "Carga de archivo de onda",
  cancelUpload: "Cancelar carga de archivo de onda",
  openWaveFile: "Abrir archivo de onda",
  getOperateReportEnd: "Fin de obtener informe de operación",
  getAuditReportEnd: "Fin de obtener informe de auditoría",
  workbookName: "informe",
  exportContent: "Campos de contenido de exportación",
  headers: {
    reportId: "ID de Informe",
    reportTime: "Tiempo de Informe",
    description: "Descripción",
    name: "Nombre",
    time: "Tiempo",
    operateAddress: "Dirección de Operación",
    operateParam: "Parámetro de Operación",
    value: "Valor",
    step: "Paso",
    source: "Fuente",
    sourceType: "Tipo de Fuente",
    result: "Resultado",
    module: "Módulo",
    message: "Mensaje",
    type: "Tipo",
    level: "Nivel",
    origin: "Origen",
    user: "Usuario"
  }
};
