/**
 * Mensajes de error - Español
 */
export default {
  success: "Éxito",
  deviceNotConnected: "Dispositivo no conectado",
  invalidParam: "Parámetro inválido",
  operateFailed: "Operación fallida",
  noData: "Sin datos",
  internalError: "Error interno",
  connectionExists: "La conexión ya existe",
  fileContentEmpty: "El contenido del archivo está vacío",
  deviceNotConnectedOrDisconnected: "Dispositivo no conectado o desconectado.",
  getServiceErrorInfo: "Error al obtener información de error correspondiente",
  saveReportFileError: "Error al guardar archivo de informe rpt",
  getConfigureListError: "Error al obtener lista de configuración",
  loadConfigureError: "Error al cargar configuración",
  cancelUploadError: "Error al cancelar carga de archivo de onda",
  openWaveFileError: "Error al abrir archivo de onda",
  getFileDataSuccess: "¡Contenido de datos del archivo recuperado con éxito!",
  getHistoryReportError: "Error al obtener informe histórico",
  getSuccessful: "Obtenido con éxito",
  errorHandledInCatch: "Error manejado en catch",
  waveFileNotFound: "Archivo de onda no encontrado",
  waveFileSizeZero: "El tamaño del archivo de onda es 0",
  uploadException: "Excepción de carga",
  uploadFinished: "Carga terminada",
  saveReportXlsxError: "Error al guardar archivo de informe xlsx",
  invalidXmlStructure: "Estructura XML inválida: falta configVersion",
  failedToGetTreeMenu: "Error al obtener menú de árbol",
  missingHeaders: "Faltan encabezados",
  excelParseFailed: "Error al analizar Excel",
  paramModifyError: "Error de modificación de parámetro, elementos de error son:",
  paramConfirmError: "Error de confirmación de parámetro, razón:",
  errorReason: ", razón del error:",
  invalidValue: "valor inválido",
  errorItem: "elemento de error:",
  description: ", descripción",
  excelFileParseError: "Error al analizar archivo Excel:",
  csvFileParseError: "Error al analizar archivo CSV:",
  xmlFileParseError: "Error al analizar archivo XML:",
  connectionFailed: "Conexión fallida",
  connectionTimeout: "Tiempo de conexión agotado",
  dataLengthMismatch: "La longitud de datos vkeys devuelta por el dispositivo no coincide con la longitud de la solicitud",
  invalidFilePath: "Ruta de archivo inválida. Proporcione una ruta de archivo .xlsx válida.",
  failedToCreateDirectory: "Error al crear directorio",
  failedToExportData: "Error al exportar datos",
  worksheetNotFound: "El archivo Excel no contiene la hoja de trabajo especificada.",
  noHeaders: "El archivo Excel no contiene encabezados.",
  parseExcelUnknownError: "Error al analizar el archivo Excel con un error desconocido",
  errorParsingXml: "Error al analizar XML",
  failedToUpdateXmlFile: "Error al actualizar archivo XML",
  // Mensajes de error del controlador
  addDeviceConfigFailed: "Error al agregar configuración de dispositivo",
  updateDeviceConfigFailed: "Error al actualizar configuración de dispositivo",
  deleteDeviceConfigFailed: "Error al eliminar configuración de dispositivo",
  deleteSuccess: "Eliminación exitosa",
  deleteFailed: "Eliminación falló",
  getDeviceList: "Obtener dispositivos",
  getDeviceConfigListFailed: "Error al obtener lista de configuración de dispositivo",
  exportFilePathEmpty: "La ruta del archivo de exportación está vacía",
  exportFileExtensionError: "Error en la extensión del archivo de exportación",
  exportFolderCreateFailed: "Error al crear carpeta de exportación",
  variableNameEmpty: "El nombre de la variable no puede estar vacío",
  exportPathEmpty: "La ruta de exportación no puede estar vacía",
  importPathEmpty: "La ruta de importación no puede estar vacía",
  deviceAlreadyConnected: "Dispositivo ya conectado"
};
