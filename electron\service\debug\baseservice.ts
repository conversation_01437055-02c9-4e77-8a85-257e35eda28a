import { IECResult } from "iec-common/dist/data/iecdata";
import { UpadRcpClient } from "iec-upadrpc/dist/src/UpadRpcClient";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
/**
 * <AUTHOR>
 * @version 1.0 2025-04-24
 */
export abstract class BaseService {
  getClient(id: string): IECResult<UpadRcpClient> {
    logger.debug(
      `[BaseService] getClient - ${t("services.base.getClientStart")}: ${id}`
    );
    const result = new IECResult<UpadRcpClient>();
    result.code = 0;
    const singleGlobalDeviceInfo =
      GlobalDeviceData.getInstance().deviceInfoMap.get(id);
    if (!singleGlobalDeviceInfo) {
      logger.warn(
        `[BaseService] getClient - ${t("services.base.deviceNotFound")}: ${id}`
      );
      return result;
    }
    const client = singleGlobalDeviceInfo.deviceClient;
    if (!client || !client.isConnected()) {
      logger.warn(
        `[BaseService] getClient - ${t("services.base.deviceNotConnected")}: ${id}`
      );
      return result;
    }
    result.code = 1;
    result.data = client;
    logger.debug(
      `[BaseService] getClient - ${t("services.base.getClientSuccess")}: ${id}`
    );
    return result;
  }
}
